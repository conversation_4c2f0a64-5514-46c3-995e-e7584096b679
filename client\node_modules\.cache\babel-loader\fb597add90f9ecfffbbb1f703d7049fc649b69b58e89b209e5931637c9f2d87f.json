{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\BingoBoard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\n\n// Import sub-components (we'll extract these later)\nimport BingoSquare from './BingoSquare';\nimport PointsDisplay from './PointsDisplay';\nimport PortraitOverlay from './PortraitOverlay';\nimport ConfirmationModal from './ConfirmationModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BingoBoard = ({\n  boardData,\n  progressData,\n  isReadOnly,\n  userId,\n  boardId\n}) => {\n  _s();\n  const [characters, setCharacters] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [markedCells, setMarkedCells] = useState(new Set());\n  const [userImages, setUserImages] = useState({});\n  const [selectedCharacter, setSelectedCharacter] = useState(null);\n  const [sourcePosition, setSourcePosition] = useState(null);\n  const [showRefreshConfirmation, setShowRefreshConfirmation] = useState(false);\n  const [score, setScore] = useState(0);\n\n  // Initialize board with data\n  useEffect(() => {\n    if (boardData && progressData) {\n      try {\n        // Sort the characters by slot number\n        const sortedCharacters = [...boardData].sort((a, b) => a.slot - b.slot);\n        setCharacters(sortedCharacters);\n\n        // Set marked cells from progress data\n        const markedSet = new Set(progressData.marked_cells);\n        setMarkedCells(markedSet);\n\n        // Set user images from progress data\n        setUserImages(progressData.user_images || {});\n\n        // Set score from progress data\n        setScore(progressData.score);\n        setLoading(false);\n      } catch (error) {\n        console.error('Error initializing board:', error);\n        setError(error.message);\n        setLoading(false);\n      }\n    }\n  }, [boardData, progressData]);\n\n  // Save progress when marked cells change\n  useEffect(() => {\n    const saveProgress = async () => {\n      if (!isReadOnly && userId && boardId && characters.length > 0) {\n        try {\n          // Calculate score\n          const newScore = calculateTotalPoints();\n\n          // Save to backend\n          await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/progress`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              marked_cells: Array.from(markedCells),\n              user_images: userImages,\n              score: newScore\n            })\n          });\n\n          // Update local score state\n          setScore(newScore);\n        } catch (error) {\n          console.error('Error saving progress:', error);\n        }\n      }\n    };\n\n    // Only save if we have marked cells and it's not read-only\n    if (markedCells.size > 0 && !isReadOnly) {\n      saveProgress();\n    }\n  }, [markedCells, userImages, isReadOnly, userId, boardId, characters]);\n\n  // Function to fetch characters and generate a new board\n  const fetchCharactersAndGenerateBoard = async () => {\n    try {\n      setLoading(true);\n\n      // Create a new board for the user\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/board`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n      const newBoardData = await response.json();\n\n      // Sort the characters by slot number\n      const sortedCharacters = [...newBoardData.board_data].sort((a, b) => a.slot - b.slot);\n      setCharacters(sortedCharacters);\n\n      // Get the new progress (should have FREE space marked)\n      const progressResponse = await fetch(`http://localhost:5000/api/users/${userId}/boards/${newBoardData.id}/progress`);\n      if (!progressResponse.ok) {\n        throw new Error(`HTTP error! Status: ${progressResponse.status}`);\n      }\n      const newProgressData = await progressResponse.json();\n\n      // Set marked cells from new progress data\n      const markedSet = new Set(newProgressData.marked_cells);\n      setMarkedCells(markedSet);\n\n      // Set user images from new progress data\n      setUserImages(newProgressData.user_images || {});\n\n      // Set score from new progress data\n      setScore(newProgressData.score);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error generating board:', error);\n      setError(error.message);\n      setLoading(false);\n    }\n  };\n\n  // Handle refresh button click\n  const handleRefreshClick = () => {\n    if (isReadOnly) return; // Disable refresh for read-only mode\n    setShowRefreshConfirmation(true);\n  };\n\n  // Handle cancel refresh\n  const handleCancelRefresh = () => {\n    setShowRefreshConfirmation(false);\n  };\n\n  // Handle confirm refresh\n  const handleConfirmRefresh = () => {\n    // Reset state\n    setMarkedCells(new Set());\n    setUserImages({});\n    setSelectedCharacter(null);\n    setSourcePosition(null);\n    setShowRefreshConfirmation(false);\n\n    // Generate a new board\n    fetchCharactersAndGenerateBoard();\n  };\n\n  // Handles clicking on a square to mark/unmark it\n  const handleSquareClick = index => {\n    if (isReadOnly) return; // Disable marking for read-only mode\n\n    const character = characters[index];\n\n    // Cannot unmark the \"FREE\" space\n    if (character.rarity === 'FREE') {\n      return;\n    }\n    const newMarkedCells = new Set(markedCells);\n    if (newMarkedCells.has(index)) {\n      newMarkedCells.delete(index); // Unmark if already marked\n    } else {\n      newMarkedCells.add(index); // Mark if not marked\n    }\n    setMarkedCells(newMarkedCells);\n  };\n\n  // Handle clicking on a thumbnail to show the portrait\n  const handlePortraitClick = (character, position, index) => {\n    setSourcePosition(position);\n    setSelectedCharacter({\n      ...character,\n      index\n    });\n  };\n\n  // Handle closing the portrait overlay\n  const handleClosePortrait = () => {\n    setSelectedCharacter(null);\n    setSourcePosition(null);\n  };\n\n  // Handle claiming or unclaiming a character\n  const handleClaimCharacter = (imagePath = null) => {\n    if (isReadOnly) return; // Disable claiming for read-only mode\n\n    if (selectedCharacter && selectedCharacter.index !== undefined) {\n      const index = selectedCharacter.index;\n      const newMarkedCells = new Set(markedCells);\n      const newUserImages = {\n        ...userImages\n      };\n\n      // Toggle the claim status (except for FREE square)\n      if (characters[index].rarity === 'FREE') {\n        // FREE square should always remain claimed\n        newMarkedCells.add(index);\n      } else if (newMarkedCells.has(index) && imagePath === null) {\n        // Unclaim if already claimed and no new image provided\n        newMarkedCells.delete(index);\n        delete newUserImages[index];\n      } else if (imagePath) {\n        // Claim with uploaded image\n        newMarkedCells.add(index);\n        newUserImages[index] = imagePath;\n      } else {\n        // This shouldn't happen with the new upload flow, but keep for safety\n        newMarkedCells.add(index);\n      }\n      setMarkedCells(newMarkedCells);\n      setUserImages(newUserImages);\n    }\n\n    // Close the overlay\n    setSelectedCharacter(null);\n  };\n\n  // Point values for each rarity\n  const RARITY_POINTS = {\n    \"FREE\": 1,\n    \"R\": 2,\n    \"SR\": 3,\n    \"SSR\": 4,\n    \"UR+\": 6\n  };\n\n  // Calculate points from marked cells\n  const calculateBasePoints = () => {\n    let totalPoints = 0;\n    markedCells.forEach(index => {\n      if (index >= 0 && index < characters.length) {\n        const character = characters[index];\n        totalPoints += RARITY_POINTS[character.rarity];\n      }\n    });\n    return totalPoints;\n  };\n\n  // Check if there's a bingo (5 in a row, column, or diagonal)\n  const checkForBingos = () => {\n    const bingoBonus = 5; // Bonus points for each bingo\n    let bingoCount = 0;\n\n    // Convert markedCells set to a 5x5 grid for easier checking\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\n    markedCells.forEach(index => {\n      const row = Math.floor(index / 5);\n      const col = index % 5;\n      grid[row][col] = true;\n    });\n\n    // Check rows\n    for (let row = 0; row < 5; row++) {\n      if (grid[row].every(cell => cell)) {\n        bingoCount++;\n      }\n    }\n\n    // Check columns\n    for (let col = 0; col < 5; col++) {\n      if (grid.every(row => row[col])) {\n        bingoCount++;\n      }\n    }\n\n    // Check main diagonal (top-left to bottom-right)\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\n      bingoCount++;\n    }\n\n    // Check other diagonal (top-right to bottom-left)\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\n      bingoCount++;\n    }\n    return bingoCount * bingoBonus;\n  };\n\n  // Calculate total points\n  const calculateTotalPoints = () => {\n    const basePoints = calculateBasePoints();\n    const bingoPoints = checkForBingos();\n    return basePoints + bingoPoints;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-message\",\n      children: \"Loading board...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bingo-board\",\n      children: characters.map((character, index) => /*#__PURE__*/_jsxDEV(BingoSquare, {\n        index: index,\n        character: character,\n        isMarked: markedCells.has(index),\n        onPortraitClick: handlePortraitClick,\n        isReadOnly: isReadOnly,\n        userImage: userImages[index]\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PointsDisplay, {\n      characters: characters,\n      markedCells: markedCells,\n      onRefreshClick: handleRefreshClick,\n      isReadOnly: isReadOnly,\n      score: score\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 7\n    }, this), selectedCharacter && /*#__PURE__*/_jsxDEV(PortraitOverlay, {\n      character: selectedCharacter,\n      onClose: handleClosePortrait,\n      onClaim: handleClaimCharacter,\n      sourcePosition: sourcePosition,\n      isClaimed: selectedCharacter.index !== undefined && markedCells.has(selectedCharacter.index),\n      isReadOnly: isReadOnly,\n      userId: userId,\n      boardId: boardId,\n      squareIndex: selectedCharacter.index\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this), showRefreshConfirmation && /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      onCancel: handleCancelRefresh,\n      onConfirm: handleConfirmRefresh\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(BingoBoard, \"szzPFmYC1bE/mKE3TeFkIEj1ESg=\");\n_c = BingoBoard;\nexport default BingoBoard;\nvar _c;\n$RefreshReg$(_c, \"BingoBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "BingoSquare", "PointsDisplay", "PortraitOverlay", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BingoBoard", "boardData", "progressData", "isReadOnly", "userId", "boardId", "_s", "characters", "setChara<PERSON><PERSON>", "loading", "setLoading", "error", "setError", "<PERSON><PERSON><PERSON><PERSON>", "setMarkedCells", "Set", "userImages", "setUserImages", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedCharacter", "sourcePosition", "setSourcePosition", "showRefreshConfirmation", "setShowRefreshConfirmation", "score", "setScore", "sortedCharacters", "sort", "a", "b", "slot", "markedSet", "marked_cells", "user_images", "console", "message", "saveProgress", "length", "newScore", "calculateTotalPoints", "fetch", "method", "headers", "body", "JSON", "stringify", "Array", "from", "size", "fetchCharactersAndGenerateBoard", "response", "ok", "Error", "status", "newBoardData", "json", "board_data", "progressResponse", "id", "newProgressData", "handleRefreshClick", "handleCancelRefresh", "handleConfirmRefresh", "handleSquareClick", "index", "character", "rarity", "newMarkedCells", "has", "delete", "add", "handlePortraitClick", "position", "handleClosePortrait", "handleClaimCharacter", "imagePath", "undefined", "newUserImages", "RARITY_POINTS", "calculateBasePoints", "totalPoints", "for<PERSON>ach", "checkForBingos", "bingoBonus", "bingoCount", "grid", "fill", "map", "row", "Math", "floor", "col", "every", "cell", "basePoints", "bingoPoints", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isMarked", "onPortraitClick", "userImage", "onRefreshClick", "onClose", "onClaim", "isClaimed", "squareIndex", "onCancel", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/BingoBoard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\n\r\n// Import sub-components (we'll extract these later)\r\nimport BingoSquare from './BingoSquare';\r\nimport PointsDisplay from './PointsDisplay';\r\nimport PortraitOverlay from './PortraitOverlay';\r\nimport ConfirmationModal from './ConfirmationModal';\r\n\r\nconst BingoBoard = ({ boardData, progressData, isReadOnly, userId, boardId }) => {\r\n  const [characters, setCharacters] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [markedCells, setMarkedCells] = useState(new Set());\r\n  const [userImages, setUserImages] = useState({});\r\n  const [selectedCharacter, setSelectedCharacter] = useState(null);\r\n  const [sourcePosition, setSourcePosition] = useState(null);\r\n  const [showRefreshConfirmation, setShowRefreshConfirmation] = useState(false);\r\n  const [score, setScore] = useState(0);\r\n\r\n  // Initialize board with data\r\n  useEffect(() => {\r\n    if (boardData && progressData) {\r\n      try {\r\n        // Sort the characters by slot number\r\n        const sortedCharacters = [...boardData].sort((a, b) => a.slot - b.slot);\r\n        setCharacters(sortedCharacters);\r\n\r\n        // Set marked cells from progress data\r\n        const markedSet = new Set(progressData.marked_cells);\r\n        setMarkedCells(markedSet);\r\n\r\n        // Set user images from progress data\r\n        setUserImages(progressData.user_images || {});\r\n\r\n        // Set score from progress data\r\n        setScore(progressData.score);\r\n\r\n        setLoading(false);\r\n      } catch (error) {\r\n        console.error('Error initializing board:', error);\r\n        setError(error.message);\r\n        setLoading(false);\r\n      }\r\n    }\r\n  }, [boardData, progressData]);\r\n\r\n  // Save progress when marked cells change\r\n  useEffect(() => {\r\n    const saveProgress = async () => {\r\n      if (!isReadOnly && userId && boardId && characters.length > 0) {\r\n        try {\r\n          // Calculate score\r\n          const newScore = calculateTotalPoints();\r\n          \r\n          // Save to backend\r\n          await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/progress`, {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({\r\n              marked_cells: Array.from(markedCells),\r\n              user_images: userImages,\r\n              score: newScore\r\n            }),\r\n          });\r\n          \r\n          // Update local score state\r\n          setScore(newScore);\r\n        } catch (error) {\r\n          console.error('Error saving progress:', error);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Only save if we have marked cells and it's not read-only\r\n    if (markedCells.size > 0 && !isReadOnly) {\r\n      saveProgress();\r\n    }\r\n  }, [markedCells, userImages, isReadOnly, userId, boardId, characters]);\r\n\r\n  // Function to fetch characters and generate a new board\r\n  const fetchCharactersAndGenerateBoard = async () => {\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Create a new board for the user\r\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/board`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! Status: ${response.status}`);\r\n      }\r\n\r\n      const newBoardData = await response.json();\r\n\r\n      // Sort the characters by slot number\r\n      const sortedCharacters = [...newBoardData.board_data].sort((a, b) => a.slot - b.slot);\r\n      setCharacters(sortedCharacters);\r\n\r\n      // Get the new progress (should have FREE space marked)\r\n      const progressResponse = await fetch(`http://localhost:5000/api/users/${userId}/boards/${newBoardData.id}/progress`);\r\n      \r\n      if (!progressResponse.ok) {\r\n        throw new Error(`HTTP error! Status: ${progressResponse.status}`);\r\n      }\r\n      \r\n      const newProgressData = await progressResponse.json();\r\n      \r\n      // Set marked cells from new progress data\r\n      const markedSet = new Set(newProgressData.marked_cells);\r\n      setMarkedCells(markedSet);\r\n\r\n      // Set user images from new progress data\r\n      setUserImages(newProgressData.user_images || {});\r\n\r\n      // Set score from new progress data\r\n      setScore(newProgressData.score);\r\n\r\n      setLoading(false);\r\n    } catch (error) {\r\n      console.error('Error generating board:', error);\r\n      setError(error.message);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle refresh button click\r\n  const handleRefreshClick = () => {\r\n    if (isReadOnly) return; // Disable refresh for read-only mode\r\n    setShowRefreshConfirmation(true);\r\n  };\r\n\r\n  // Handle cancel refresh\r\n  const handleCancelRefresh = () => {\r\n    setShowRefreshConfirmation(false);\r\n  };\r\n\r\n  // Handle confirm refresh\r\n  const handleConfirmRefresh = () => {\r\n    // Reset state\r\n    setMarkedCells(new Set());\r\n    setUserImages({});\r\n    setSelectedCharacter(null);\r\n    setSourcePosition(null);\r\n    setShowRefreshConfirmation(false);\r\n\r\n    // Generate a new board\r\n    fetchCharactersAndGenerateBoard();\r\n  };\r\n\r\n  // Handles clicking on a square to mark/unmark it\r\n  const handleSquareClick = (index) => {\r\n    if (isReadOnly) return; // Disable marking for read-only mode\r\n    \r\n    const character = characters[index];\r\n\r\n    // Cannot unmark the \"FREE\" space\r\n    if (character.rarity === 'FREE') {\r\n      return;\r\n    }\r\n\r\n    const newMarkedCells = new Set(markedCells);\r\n    if (newMarkedCells.has(index)) {\r\n      newMarkedCells.delete(index); // Unmark if already marked\r\n    } else {\r\n      newMarkedCells.add(index); // Mark if not marked\r\n    }\r\n    setMarkedCells(newMarkedCells);\r\n  };\r\n\r\n  // Handle clicking on a thumbnail to show the portrait\r\n  const handlePortraitClick = (character, position, index) => {\r\n    setSourcePosition(position);\r\n    setSelectedCharacter({...character, index});\r\n  };\r\n\r\n  // Handle closing the portrait overlay\r\n  const handleClosePortrait = () => {\r\n    setSelectedCharacter(null);\r\n    setSourcePosition(null);\r\n  };\r\n\r\n  // Handle claiming or unclaiming a character\r\n  const handleClaimCharacter = (imagePath = null) => {\r\n    if (isReadOnly) return; // Disable claiming for read-only mode\r\n\r\n    if (selectedCharacter && selectedCharacter.index !== undefined) {\r\n      const index = selectedCharacter.index;\r\n      const newMarkedCells = new Set(markedCells);\r\n      const newUserImages = { ...userImages };\r\n\r\n      // Toggle the claim status (except for FREE square)\r\n      if (characters[index].rarity === 'FREE') {\r\n        // FREE square should always remain claimed\r\n        newMarkedCells.add(index);\r\n      } else if (newMarkedCells.has(index) && imagePath === null) {\r\n        // Unclaim if already claimed and no new image provided\r\n        newMarkedCells.delete(index);\r\n        delete newUserImages[index];\r\n      } else if (imagePath) {\r\n        // Claim with uploaded image\r\n        newMarkedCells.add(index);\r\n        newUserImages[index] = imagePath;\r\n      } else {\r\n        // This shouldn't happen with the new upload flow, but keep for safety\r\n        newMarkedCells.add(index);\r\n      }\r\n\r\n      setMarkedCells(newMarkedCells);\r\n      setUserImages(newUserImages);\r\n    }\r\n\r\n    // Close the overlay\r\n    setSelectedCharacter(null);\r\n  };\r\n\r\n  // Point values for each rarity\r\n  const RARITY_POINTS = {\r\n    \"FREE\": 1,\r\n    \"R\": 2,\r\n    \"SR\": 3,\r\n    \"SSR\": 4,\r\n    \"UR+\": 6\r\n  };\r\n\r\n  // Calculate points from marked cells\r\n  const calculateBasePoints = () => {\r\n    let totalPoints = 0;\r\n    markedCells.forEach(index => {\r\n      if (index >= 0 && index < characters.length) {\r\n        const character = characters[index];\r\n        totalPoints += RARITY_POINTS[character.rarity];\r\n      }\r\n    });\r\n    return totalPoints;\r\n  };\r\n\r\n  // Check if there's a bingo (5 in a row, column, or diagonal)\r\n  const checkForBingos = () => {\r\n    const bingoBonus = 5; // Bonus points for each bingo\r\n    let bingoCount = 0;\r\n\r\n    // Convert markedCells set to a 5x5 grid for easier checking\r\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\r\n    markedCells.forEach(index => {\r\n      const row = Math.floor(index / 5);\r\n      const col = index % 5;\r\n      grid[row][col] = true;\r\n    });\r\n\r\n    // Check rows\r\n    for (let row = 0; row < 5; row++) {\r\n      if (grid[row].every(cell => cell)) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check columns\r\n    for (let col = 0; col < 5; col++) {\r\n      if (grid.every(row => row[col])) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check main diagonal (top-left to bottom-right)\r\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    // Check other diagonal (top-right to bottom-left)\r\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    return bingoCount * bingoBonus;\r\n  };\r\n\r\n  // Calculate total points\r\n  const calculateTotalPoints = () => {\r\n    const basePoints = calculateBasePoints();\r\n    const bingoPoints = checkForBingos();\r\n    return basePoints + bingoPoints;\r\n  };\r\n\r\n  if (loading) {\r\n    return <div className=\"loading-message\">Loading board...</div>;\r\n  }\r\n\r\n  if (error) {\r\n    return <div className=\"error-message\">Error: {error}</div>;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"bingo-board\">\r\n        {characters.map((character, index) => (\r\n          <BingoSquare\r\n            key={index}\r\n            index={index}\r\n            character={character}\r\n            isMarked={markedCells.has(index)}\r\n            onPortraitClick={handlePortraitClick}\r\n            isReadOnly={isReadOnly}\r\n            userImage={userImages[index]}\r\n          />\r\n        ))}\r\n      </div>\r\n\r\n      <PointsDisplay\r\n        characters={characters}\r\n        markedCells={markedCells}\r\n        onRefreshClick={handleRefreshClick}\r\n        isReadOnly={isReadOnly}\r\n        score={score}\r\n      />\r\n\r\n      {selectedCharacter && (\r\n        <PortraitOverlay\r\n          character={selectedCharacter}\r\n          onClose={handleClosePortrait}\r\n          onClaim={handleClaimCharacter}\r\n          sourcePosition={sourcePosition}\r\n          isClaimed={selectedCharacter.index !== undefined && markedCells.has(selectedCharacter.index)}\r\n          isReadOnly={isReadOnly}\r\n          userId={userId}\r\n          boardId={boardId}\r\n          squareIndex={selectedCharacter.index}\r\n        />\r\n      )}\r\n\r\n      {showRefreshConfirmation && (\r\n        <ConfirmationModal\r\n          onCancel={handleCancelRefresh}\r\n          onConfirm={handleConfirmRefresh}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default BingoBoard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;;AAElD;AACA,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,SAAS;EAAEC,YAAY;EAAEC,UAAU;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,IAAIyB,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;;EAErC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIU,SAAS,IAAIC,YAAY,EAAE;MAC7B,IAAI;QACF;QACA,MAAMwB,gBAAgB,GAAG,CAAC,GAAGzB,SAAS,CAAC,CAAC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,CAAC;QACvEtB,aAAa,CAACkB,gBAAgB,CAAC;;QAE/B;QACA,MAAMK,SAAS,GAAG,IAAIhB,GAAG,CAACb,YAAY,CAAC8B,YAAY,CAAC;QACpDlB,cAAc,CAACiB,SAAS,CAAC;;QAEzB;QACAd,aAAa,CAACf,YAAY,CAAC+B,WAAW,IAAI,CAAC,CAAC,CAAC;;QAE7C;QACAR,QAAQ,CAACvB,YAAY,CAACsB,KAAK,CAAC;QAE5Bd,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACduB,OAAO,CAACvB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDC,QAAQ,CAACD,KAAK,CAACwB,OAAO,CAAC;QACvBzB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACT,SAAS,EAAEC,YAAY,CAAC,CAAC;;EAE7B;EACAX,SAAS,CAAC,MAAM;IACd,MAAM6C,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI,CAACjC,UAAU,IAAIC,MAAM,IAAIC,OAAO,IAAIE,UAAU,CAAC8B,MAAM,GAAG,CAAC,EAAE;QAC7D,IAAI;UACF;UACA,MAAMC,QAAQ,GAAGC,oBAAoB,CAAC,CAAC;;UAEvC;UACA,MAAMC,KAAK,CAAC,mCAAmCpC,MAAM,WAAWC,OAAO,WAAW,EAAE;YAClFoC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;cACnBb,YAAY,EAAEc,KAAK,CAACC,IAAI,CAAClC,WAAW,CAAC;cACrCoB,WAAW,EAAEjB,UAAU;cACvBQ,KAAK,EAAEc;YACT,CAAC;UACH,CAAC,CAAC;;UAEF;UACAb,QAAQ,CAACa,QAAQ,CAAC;QACpB,CAAC,CAAC,OAAO3B,KAAK,EAAE;UACduB,OAAO,CAACvB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF;IACF,CAAC;;IAED;IACA,IAAIE,WAAW,CAACmC,IAAI,GAAG,CAAC,IAAI,CAAC7C,UAAU,EAAE;MACvCiC,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACvB,WAAW,EAAEG,UAAU,EAAEb,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEE,UAAU,CAAC,CAAC;;EAEtE;EACA,MAAM0C,+BAA+B,GAAG,MAAAA,CAAA,KAAY;IAClD,IAAI;MACFvC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwC,QAAQ,GAAG,MAAMV,KAAK,CAAC,mCAAmCpC,MAAM,QAAQ,EAAE;QAC9EqC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACQ,QAAQ,CAACC,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBF,QAAQ,CAACG,MAAM,EAAE,CAAC;MAC3D;MAEA,MAAMC,YAAY,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;;MAE1C;MACA,MAAM7B,gBAAgB,GAAG,CAAC,GAAG4B,YAAY,CAACE,UAAU,CAAC,CAAC7B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,CAAC;MACrFtB,aAAa,CAACkB,gBAAgB,CAAC;;MAE/B;MACA,MAAM+B,gBAAgB,GAAG,MAAMjB,KAAK,CAAC,mCAAmCpC,MAAM,WAAWkD,YAAY,CAACI,EAAE,WAAW,CAAC;MAEpH,IAAI,CAACD,gBAAgB,CAACN,EAAE,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,uBAAuBK,gBAAgB,CAACJ,MAAM,EAAE,CAAC;MACnE;MAEA,MAAMM,eAAe,GAAG,MAAMF,gBAAgB,CAACF,IAAI,CAAC,CAAC;;MAErD;MACA,MAAMxB,SAAS,GAAG,IAAIhB,GAAG,CAAC4C,eAAe,CAAC3B,YAAY,CAAC;MACvDlB,cAAc,CAACiB,SAAS,CAAC;;MAEzB;MACAd,aAAa,CAAC0C,eAAe,CAAC1B,WAAW,IAAI,CAAC,CAAC,CAAC;;MAEhD;MACAR,QAAQ,CAACkC,eAAe,CAACnC,KAAK,CAAC;MAE/Bd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACduB,OAAO,CAACvB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,QAAQ,CAACD,KAAK,CAACwB,OAAO,CAAC;MACvBzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIzD,UAAU,EAAE,OAAO,CAAC;IACxBoB,0BAA0B,CAAC,IAAI,CAAC;EAClC,CAAC;;EAED;EACA,MAAMsC,mBAAmB,GAAGA,CAAA,KAAM;IAChCtC,0BAA0B,CAAC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMuC,oBAAoB,GAAGA,CAAA,KAAM;IACjC;IACAhD,cAAc,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IACzBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,0BAA0B,CAAC,KAAK,CAAC;;IAEjC;IACA0B,+BAA+B,CAAC,CAAC;EACnC,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAIC,KAAK,IAAK;IACnC,IAAI7D,UAAU,EAAE,OAAO,CAAC;;IAExB,MAAM8D,SAAS,GAAG1D,UAAU,CAACyD,KAAK,CAAC;;IAEnC;IACA,IAAIC,SAAS,CAACC,MAAM,KAAK,MAAM,EAAE;MAC/B;IACF;IAEA,MAAMC,cAAc,GAAG,IAAIpD,GAAG,CAACF,WAAW,CAAC;IAC3C,IAAIsD,cAAc,CAACC,GAAG,CAACJ,KAAK,CAAC,EAAE;MAC7BG,cAAc,CAACE,MAAM,CAACL,KAAK,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM;MACLG,cAAc,CAACG,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC;IAC7B;IACAlD,cAAc,CAACqD,cAAc,CAAC;EAChC,CAAC;;EAED;EACA,MAAMI,mBAAmB,GAAGA,CAACN,SAAS,EAAEO,QAAQ,EAAER,KAAK,KAAK;IAC1D3C,iBAAiB,CAACmD,QAAQ,CAAC;IAC3BrD,oBAAoB,CAAC;MAAC,GAAG8C,SAAS;MAAED;IAAK,CAAC,CAAC;EAC7C,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAGA,CAAA,KAAM;IAChCtD,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMqD,oBAAoB,GAAGA,CAACC,SAAS,GAAG,IAAI,KAAK;IACjD,IAAIxE,UAAU,EAAE,OAAO,CAAC;;IAExB,IAAIe,iBAAiB,IAAIA,iBAAiB,CAAC8C,KAAK,KAAKY,SAAS,EAAE;MAC9D,MAAMZ,KAAK,GAAG9C,iBAAiB,CAAC8C,KAAK;MACrC,MAAMG,cAAc,GAAG,IAAIpD,GAAG,CAACF,WAAW,CAAC;MAC3C,MAAMgE,aAAa,GAAG;QAAE,GAAG7D;MAAW,CAAC;;MAEvC;MACA,IAAIT,UAAU,CAACyD,KAAK,CAAC,CAACE,MAAM,KAAK,MAAM,EAAE;QACvC;QACAC,cAAc,CAACG,GAAG,CAACN,KAAK,CAAC;MAC3B,CAAC,MAAM,IAAIG,cAAc,CAACC,GAAG,CAACJ,KAAK,CAAC,IAAIW,SAAS,KAAK,IAAI,EAAE;QAC1D;QACAR,cAAc,CAACE,MAAM,CAACL,KAAK,CAAC;QAC5B,OAAOa,aAAa,CAACb,KAAK,CAAC;MAC7B,CAAC,MAAM,IAAIW,SAAS,EAAE;QACpB;QACAR,cAAc,CAACG,GAAG,CAACN,KAAK,CAAC;QACzBa,aAAa,CAACb,KAAK,CAAC,GAAGW,SAAS;MAClC,CAAC,MAAM;QACL;QACAR,cAAc,CAACG,GAAG,CAACN,KAAK,CAAC;MAC3B;MAEAlD,cAAc,CAACqD,cAAc,CAAC;MAC9BlD,aAAa,CAAC4D,aAAa,CAAC;IAC9B;;IAEA;IACA1D,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM2D,aAAa,GAAG;IACpB,MAAM,EAAE,CAAC;IACT,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,WAAW,GAAG,CAAC;IACnBnE,WAAW,CAACoE,OAAO,CAACjB,KAAK,IAAI;MAC3B,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGzD,UAAU,CAAC8B,MAAM,EAAE;QAC3C,MAAM4B,SAAS,GAAG1D,UAAU,CAACyD,KAAK,CAAC;QACnCgB,WAAW,IAAIF,aAAa,CAACb,SAAS,CAACC,MAAM,CAAC;MAChD;IACF,CAAC,CAAC;IACF,OAAOc,WAAW;EACpB,CAAC;;EAED;EACA,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,UAAU,GAAG,CAAC,CAAC,CAAC;IACtB,IAAIC,UAAU,GAAG,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGvC,KAAK,CAAC,CAAC,CAAC,CAACwC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,MAAMzC,KAAK,CAAC,CAAC,CAAC,CAACwC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5DzE,WAAW,CAACoE,OAAO,CAACjB,KAAK,IAAI;MAC3B,MAAMwB,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC1B,KAAK,GAAG,CAAC,CAAC;MACjC,MAAM2B,GAAG,GAAG3B,KAAK,GAAG,CAAC;MACrBqB,IAAI,CAACG,GAAG,CAAC,CAACG,GAAG,CAAC,GAAG,IAAI;IACvB,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIH,IAAI,CAACG,GAAG,CAAC,CAACI,KAAK,CAACC,IAAI,IAAIA,IAAI,CAAC,EAAE;QACjCT,UAAU,EAAE;MACd;IACF;;IAEA;IACA,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIN,IAAI,CAACO,KAAK,CAACJ,GAAG,IAAIA,GAAG,CAACG,GAAG,CAAC,CAAC,EAAE;QAC/BP,UAAU,EAAE;MACd;IACF;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;IAEA,OAAOA,UAAU,GAAGD,UAAU;EAChC,CAAC;;EAED;EACA,MAAM5C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMuD,UAAU,GAAGf,mBAAmB,CAAC,CAAC;IACxC,MAAMgB,WAAW,GAAGb,cAAc,CAAC,CAAC;IACpC,OAAOY,UAAU,GAAGC,WAAW;EACjC,CAAC;EAED,IAAItF,OAAO,EAAE;IACX,oBAAOZ,OAAA;MAAKmG,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChE;EAEA,IAAI1F,KAAK,EAAE;IACT,oBAAOd,OAAA;MAAKmG,SAAS,EAAC,eAAe;MAAAC,QAAA,GAAC,SAAO,EAACtF,KAAK;IAAA;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC5D;EAEA,oBACExG,OAAA,CAAAE,SAAA;IAAAkG,QAAA,gBACEpG,OAAA;MAAKmG,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzB1F,UAAU,CAACgF,GAAG,CAAC,CAACtB,SAAS,EAAED,KAAK,kBAC/BnE,OAAA,CAACL,WAAW;QAEVwE,KAAK,EAAEA,KAAM;QACbC,SAAS,EAAEA,SAAU;QACrBqC,QAAQ,EAAEzF,WAAW,CAACuD,GAAG,CAACJ,KAAK,CAAE;QACjCuC,eAAe,EAAEhC,mBAAoB;QACrCpE,UAAU,EAAEA,UAAW;QACvBqG,SAAS,EAAExF,UAAU,CAACgD,KAAK;MAAE,GANxBA,KAAK;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENxG,OAAA,CAACJ,aAAa;MACZc,UAAU,EAAEA,UAAW;MACvBM,WAAW,EAAEA,WAAY;MACzB4F,cAAc,EAAE7C,kBAAmB;MACnCzD,UAAU,EAAEA,UAAW;MACvBqB,KAAK,EAAEA;IAAM;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,EAEDnF,iBAAiB,iBAChBrB,OAAA,CAACH,eAAe;MACduE,SAAS,EAAE/C,iBAAkB;MAC7BwF,OAAO,EAAEjC,mBAAoB;MAC7BkC,OAAO,EAAEjC,oBAAqB;MAC9BtD,cAAc,EAAEA,cAAe;MAC/BwF,SAAS,EAAE1F,iBAAiB,CAAC8C,KAAK,KAAKY,SAAS,IAAI/D,WAAW,CAACuD,GAAG,CAAClD,iBAAiB,CAAC8C,KAAK,CAAE;MAC7F7D,UAAU,EAAEA,UAAW;MACvBC,MAAM,EAAEA,MAAO;MACfC,OAAO,EAAEA,OAAQ;MACjBwG,WAAW,EAAE3F,iBAAiB,CAAC8C;IAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CACF,EAEA/E,uBAAuB,iBACtBzB,OAAA,CAACF,iBAAiB;MAChBmH,QAAQ,EAAEjD,mBAAoB;MAC9BkD,SAAS,EAAEjD;IAAqB;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF;EAAA,eACD,CAAC;AAEP,CAAC;AAAC/F,EAAA,CA/UIN,UAAU;AAAAgH,EAAA,GAAVhH,UAAU;AAiVhB,eAAeA,UAAU;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}