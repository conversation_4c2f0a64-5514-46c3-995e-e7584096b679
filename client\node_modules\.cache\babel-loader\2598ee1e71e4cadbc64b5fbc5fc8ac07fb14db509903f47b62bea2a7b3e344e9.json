{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\PortraitOverlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PortraitOverlay = ({\n  character,\n  onClose,\n  onClaim,\n  sourcePosition,\n  isClaimed,\n  isReadOnly,\n  userId,\n  boardId,\n  squareIndex\n}) => {\n  _s();\n  // Get the portrait URL from the portrait path\n  const getPortraitUrl = portraitPath => {\n    if (!portraitPath) return null;\n    return `${process.env.PUBLIC_URL}${portraitPath}`;\n  };\n  const portraitUrl = getPortraitUrl(character.Portrait);\n\n  // Get the frame overlay URL based on character rarity\n  const getFrameOverlayUrl = rarity => {\n    if (!rarity || rarity === 'FREE') return null; // No frame for FREE characters\n    return `${process.env.PUBLIC_URL}/frames/${rarity} - Portrait.png`;\n  };\n  const frameOverlayUrl = getFrameOverlayUrl(character.rarity);\n\n  // Use state to control animation classes and details visibility\n  const [isVisible, setIsVisible] = useState(false);\n  const [showDetails, setShowDetails] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploading, setUploading] = useState(false);\n  const [showUpload, setShowUpload] = useState(false);\n  const fileInputRef = useRef(null);\n\n  // Apply the animation after component mounts\n  useEffect(() => {\n    // Small delay to ensure the component is rendered before animation starts\n    const timer = setTimeout(() => {\n      setIsVisible(true);\n    }, 50);\n    return () => clearTimeout(timer);\n  }, []);\n\n  // Handle closing with animation\n  const handleClose = () => {\n    setIsVisible(false);\n    setShowDetails(false);\n    // Wait for animation to complete before actually closing\n    setTimeout(onClose, 300);\n  };\n\n  // Handle file selection\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    if (file) {\n      setSelectedFile(file);\n      setShowUpload(true);\n    }\n  };\n\n  // Handle file upload\n  const handleUpload = async () => {\n    if (!selectedFile || !userId || !boardId || squareIndex === undefined) return;\n    setUploading(true);\n    const formData = new FormData();\n    formData.append('file', selectedFile);\n    try {\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/upload/${squareIndex}`, {\n        method: 'POST',\n        body: formData\n      });\n      if (response.ok) {\n        const result = await response.json();\n        // Call onClaim with the uploaded image path\n        onClaim(result.image_path);\n        setIsVisible(false);\n        setShowDetails(false);\n        setShowUpload(false);\n      } else {\n        console.error('Upload failed');\n        alert('Failed to upload image. Please try again.');\n      }\n    } catch (error) {\n      console.error('Upload error:', error);\n      alert('Failed to upload image. Please try again.');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  // Handle claiming with animation (for unclaiming)\n  const handleClaim = () => {\n    if (isReadOnly) return; // Disable claiming for read-only mode\n\n    if (isClaimed) {\n      // If already claimed, just unclaim\n      setIsVisible(false);\n      setShowDetails(false);\n      setTimeout(() => onClaim(null), 300);\n    } else {\n      // If not claimed, show upload interface\n      setShowUpload(true);\n    }\n  };\n\n  // Handle showing/hiding details\n  const handleToggleDetails = () => {\n    setShowDetails(!showDetails);\n  };\n\n  // Get rarity value text\n  const getRarityValue = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return 'FREE (1 pt)';\n      case 'R':\n        return 'R (2 pts)';\n      case 'SR':\n        return 'SR (3 pts)';\n      case 'SSR':\n        return 'SSR (4 pts)';\n      case 'UR+':\n        return 'UR+ (6 pts)';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  // Get rarity color for details button\n  const getRarityColor = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return '#4CAF50';\n      // Green\n      case 'R':\n        return '#2196F3';\n      // Blue\n      case 'SR':\n        return '#9C27B0';\n      // Purple\n      case 'SSR':\n        return '#FF9800';\n      // Orange\n      case 'UR+':\n        return '#F44336';\n      // Red\n      default:\n        return '#2196F3';\n      // Default blue\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `portrait-overlay ${isVisible ? 'visible' : ''}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `portrait-container ${isVisible ? 'visible' : ''}`,\n      style: sourcePosition ? {\n        // If we have source position, use it for initial transform origin\n        transformOrigin: `${sourcePosition.x}px ${sourcePosition.y}px`\n      } : {},\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"close-button\",\n        onClick: handleClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"portrait-image-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: portraitUrl,\n          alt: character.Name,\n          className: \"character-portrait\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), frameOverlayUrl && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: frameOverlayUrl,\n          alt: `${character.rarity} frame`,\n          className: \"portrait-frame-overlay\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"portrait-buttons\",\n        children: [!isReadOnly && !showUpload && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `claim-button ${isClaimed ? 'unclaim' : ''}`,\n          onClick: handleClaim,\n          children: isClaimed ? 'Unclaim' : 'Claim!'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), !showUpload && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"details-button\",\n          onClick: handleToggleDetails,\n          style: {\n            backgroundColor: getRarityColor(character.rarity),\n            boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\n          },\n          children: \"Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), showUpload && !isReadOnly && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"upload-interface\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Upload Your Photo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Upload a photo of yourself with this cosplayer to claim this square!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          ref: fileInputRef,\n          onChange: handleFileSelect,\n          accept: \"image/*\",\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"upload-select-button\",\n            onClick: () => {\n              var _fileInputRef$current;\n              return (_fileInputRef$current = fileInputRef.current) === null || _fileInputRef$current === void 0 ? void 0 : _fileInputRef$current.click();\n            },\n            disabled: uploading,\n            children: selectedFile ? 'Change Photo' : 'Select Photo'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), selectedFile && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"selected-file-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Selected: \", selectedFile.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"upload-confirm-button\",\n                onClick: handleUpload,\n                disabled: uploading,\n                children: uploading ? 'Uploading...' : 'Upload & Claim'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"upload-cancel-button\",\n                onClick: () => {\n                  setShowUpload(false);\n                  setSelectedFile(null);\n                },\n                disabled: uploading,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"character-details-overlay\",\n        onClick: () => setShowDetails(false),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"character-details-content\",\n          onClick: e => e.stopPropagation() // Prevent clicks on content from closing\n          ,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 92\n            }, this), character.Name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Source:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 94\n            }, this), character.Source]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Value:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 93\n            }, this), getRarityValue(character.rarity)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"What to look for:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 104\n            }, this), character.description || \"No description available\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: getRarityColor(character.rarity)\n              },\n              children: \"Special conditions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 106\n            }, this), character.conditions || \"None\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(PortraitOverlay, \"+XvkU7TDF9hWkZrsoh0jQpxfuFc=\");\n_c = PortraitOverlay;\nexport default PortraitOverlay;\nvar _c;\n$RefreshReg$(_c, \"PortraitOverlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "PortraitOverlay", "character", "onClose", "onClaim", "sourcePosition", "isClaimed", "isReadOnly", "userId", "boardId", "squareIndex", "_s", "getPortraitUrl", "<PERSON><PERSON><PERSON>", "process", "env", "PUBLIC_URL", "portraitUrl", "Portrait", "getFrameOverlayUrl", "rarity", "frameOverlayUrl", "isVisible", "setIsVisible", "showDetails", "setShowDetails", "selectedFile", "setSelectedFile", "uploading", "setUploading", "showUpload", "setShowUpload", "fileInputRef", "timer", "setTimeout", "clearTimeout", "handleClose", "handleFileSelect", "event", "file", "target", "files", "handleUpload", "undefined", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "result", "json", "image_path", "console", "error", "alert", "handleClaim", "handleToggleDetails", "getRarityValue", "getRarityColor", "className", "children", "style", "transform<PERSON><PERSON>in", "x", "y", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "Name", "backgroundColor", "boxShadow", "type", "ref", "onChange", "accept", "display", "_fileInputRef$current", "current", "click", "disabled", "name", "e", "stopPropagation", "color", "Source", "description", "conditions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/PortraitOverlay.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\n\r\nconst PortraitOverlay = ({ character, onClose, onClaim, sourcePosition, isClaimed, isReadOnly, userId, boardId, squareIndex }) => {\r\n  // Get the portrait URL from the portrait path\r\n  const getPortraitUrl = (portraitPath) => {\r\n    if (!portraitPath) return null;\r\n    return `${process.env.PUBLIC_URL}${portraitPath}`;\r\n  };\r\n\r\n  const portraitUrl = getPortraitUrl(character.Portrait);\r\n\r\n  // Get the frame overlay URL based on character rarity\r\n  const getFrameOverlayUrl = (rarity) => {\r\n    if (!rarity || rarity === 'FREE') return null; // No frame for FREE characters\r\n    return `${process.env.PUBLIC_URL}/frames/${rarity} - Portrait.png`;\r\n  };\r\n\r\n  const frameOverlayUrl = getFrameOverlayUrl(character.rarity);\r\n\r\n  // Use state to control animation classes and details visibility\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [showDetails, setShowDetails] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [uploading, setUploading] = useState(false);\r\n  const [showUpload, setShowUpload] = useState(false);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // Apply the animation after component mounts\r\n  useEffect(() => {\r\n    // Small delay to ensure the component is rendered before animation starts\r\n    const timer = setTimeout(() => {\r\n      setIsVisible(true);\r\n    }, 50);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  // Handle closing with animation\r\n  const handleClose = () => {\r\n    setIsVisible(false);\r\n    setShowDetails(false);\r\n    // Wait for animation to complete before actually closing\r\n    setTimeout(onClose, 300);\r\n  };\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      setSelectedFile(file);\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle file upload\r\n  const handleUpload = async () => {\r\n    if (!selectedFile || !userId || !boardId || squareIndex === undefined) return;\r\n\r\n    setUploading(true);\r\n    const formData = new FormData();\r\n    formData.append('file', selectedFile);\r\n\r\n    try {\r\n      const response = await fetch(`http://localhost:5000/api/users/${userId}/boards/${boardId}/upload/${squareIndex}`, {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        // Call onClaim with the uploaded image path\r\n        onClaim(result.image_path);\r\n        setIsVisible(false);\r\n        setShowDetails(false);\r\n        setShowUpload(false);\r\n      } else {\r\n        console.error('Upload failed');\r\n        alert('Failed to upload image. Please try again.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Upload error:', error);\r\n      alert('Failed to upload image. Please try again.');\r\n    } finally {\r\n      setUploading(false);\r\n    }\r\n  };\r\n\r\n  // Handle claiming with animation (for unclaiming)\r\n  const handleClaim = () => {\r\n    if (isReadOnly) return; // Disable claiming for read-only mode\r\n\r\n    if (isClaimed) {\r\n      // If already claimed, just unclaim\r\n      setIsVisible(false);\r\n      setShowDetails(false);\r\n      setTimeout(() => onClaim(null), 300);\r\n    } else {\r\n      // If not claimed, show upload interface\r\n      setShowUpload(true);\r\n    }\r\n  };\r\n\r\n  // Handle showing/hiding details\r\n  const handleToggleDetails = () => {\r\n    setShowDetails(!showDetails);\r\n  };\r\n\r\n  // Get rarity value text\r\n  const getRarityValue = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return 'FREE (1 pt)';\r\n      case 'R':\r\n        return 'R (2 pts)';\r\n      case 'SR':\r\n        return 'SR (3 pts)';\r\n      case 'SSR':\r\n        return 'SSR (4 pts)';\r\n      case 'UR+':\r\n        return 'UR+ (6 pts)';\r\n      default:\r\n        return 'Unknown';\r\n    }\r\n  };\r\n\r\n  // Get rarity color for details button\r\n  const getRarityColor = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return '#4CAF50'; // Green\r\n      case 'R':\r\n        return '#2196F3'; // Blue\r\n      case 'SR':\r\n        return '#9C27B0'; // Purple\r\n      case 'SSR':\r\n        return '#FF9800'; // Orange\r\n      case 'UR+':\r\n        return '#F44336'; // Red\r\n      default:\r\n        return '#2196F3'; // Default blue\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`portrait-overlay ${isVisible ? 'visible' : ''}`}>\r\n      <div\r\n        className={`portrait-container ${isVisible ? 'visible' : ''}`}\r\n        style={sourcePosition ? {\r\n          // If we have source position, use it for initial transform origin\r\n          transformOrigin: `${sourcePosition.x}px ${sourcePosition.y}px`\r\n        } : {}}\r\n      >\r\n        <button className=\"close-button\" onClick={handleClose}>×</button>\r\n        <div className=\"portrait-image-container\">\r\n          <img\r\n            src={portraitUrl}\r\n            alt={character.Name}\r\n            className=\"character-portrait\"\r\n          />\r\n          {frameOverlayUrl && (\r\n            <img\r\n              src={frameOverlayUrl}\r\n              alt={`${character.rarity} frame`}\r\n              className=\"portrait-frame-overlay\"\r\n            />\r\n          )}\r\n        </div>\r\n        <div className=\"portrait-buttons\">\r\n          {!isReadOnly && !showUpload && (\r\n            <button\r\n              className={`claim-button ${isClaimed ? 'unclaim' : ''}`}\r\n              onClick={handleClaim}\r\n            >\r\n              {isClaimed ? 'Unclaim' : 'Claim!'}\r\n            </button>\r\n          )}\r\n          {!showUpload && (\r\n            <button\r\n              className=\"details-button\"\r\n              onClick={handleToggleDetails}\r\n              style={{\r\n                backgroundColor: getRarityColor(character.rarity),\r\n                boxShadow: `0 0 20px ${getRarityColor(character.rarity)}80, 0 4px 6px rgba(0, 0, 0, 0.1)`\r\n              }}\r\n            >\r\n              Details\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {/* Upload Interface */}\r\n        {showUpload && !isReadOnly && (\r\n          <div className=\"upload-interface\">\r\n            <h3>Upload Your Photo</h3>\r\n            <p>Upload a photo of yourself with this cosplayer to claim this square!</p>\r\n\r\n            <input\r\n              type=\"file\"\r\n              ref={fileInputRef}\r\n              onChange={handleFileSelect}\r\n              accept=\"image/*\"\r\n              style={{ display: 'none' }}\r\n            />\r\n\r\n            <div className=\"upload-buttons\">\r\n              <button\r\n                className=\"upload-select-button\"\r\n                onClick={() => fileInputRef.current?.click()}\r\n                disabled={uploading}\r\n              >\r\n                {selectedFile ? 'Change Photo' : 'Select Photo'}\r\n              </button>\r\n\r\n              {selectedFile && (\r\n                <div className=\"selected-file-info\">\r\n                  <p>Selected: {selectedFile.name}</p>\r\n                  <div className=\"upload-actions\">\r\n                    <button\r\n                      className=\"upload-confirm-button\"\r\n                      onClick={handleUpload}\r\n                      disabled={uploading}\r\n                    >\r\n                      {uploading ? 'Uploading...' : 'Upload & Claim'}\r\n                    </button>\r\n                    <button\r\n                      className=\"upload-cancel-button\"\r\n                      onClick={() => {\r\n                        setShowUpload(false);\r\n                        setSelectedFile(null);\r\n                      }}\r\n                      disabled={uploading}\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {showDetails && (\r\n          <div\r\n            className=\"character-details-overlay\"\r\n            onClick={() => setShowDetails(false)}\r\n          >\r\n            <div\r\n              className=\"character-details-content\"\r\n              onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing\r\n            >\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Name:</strong><br />{character.Name}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Source:</strong><br />{character.Source}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Value:</strong><br />{getRarityValue(character.rarity)}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>What to look for:</strong><br />{character.description || \"No description available\"}</p>\r\n              <p><strong style={{ color: getRarityColor(character.rarity) }}>Special conditions:</strong><br />{character.conditions || \"None\"}</p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PortraitOverlay;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,eAAe,GAAGA,CAAC;EAAEC,SAAS;EAAEC,OAAO;EAAEC,OAAO;EAAEC,cAAc;EAAEC,SAAS;EAAEC,UAAU;EAAEC,MAAM;EAAEC,OAAO;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAChI;EACA,MAAMC,cAAc,GAAIC,YAAY,IAAK;IACvC,IAAI,CAACA,YAAY,EAAE,OAAO,IAAI;IAC9B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAGH,YAAY,EAAE;EACnD,CAAC;EAED,MAAMI,WAAW,GAAGL,cAAc,CAACV,SAAS,CAACgB,QAAQ,CAAC;;EAEtD;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC,IAAI,CAACA,MAAM,IAAIA,MAAM,KAAK,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;IAC/C,OAAO,GAAGN,OAAO,CAACC,GAAG,CAACC,UAAU,WAAWI,MAAM,iBAAiB;EACpE,CAAC;EAED,MAAMC,eAAe,GAAGF,kBAAkB,CAACjB,SAAS,CAACkB,MAAM,CAAC;;EAE5D;EACA,MAAM,CAACE,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMoC,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACAD,SAAS,CAAC,MAAM;IACd;IACA,MAAMoC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BX,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,EAAE,EAAE,CAAC;IAEN,OAAO,MAAMY,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,WAAW,GAAGA,CAAA,KAAM;IACxBb,YAAY,CAAC,KAAK,CAAC;IACnBE,cAAc,CAAC,KAAK,CAAC;IACrB;IACAS,UAAU,CAAC/B,OAAO,EAAE,GAAG,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACRZ,eAAe,CAACY,IAAI,CAAC;MACrBR,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAChB,YAAY,IAAI,CAAClB,MAAM,IAAI,CAACC,OAAO,IAAIC,WAAW,KAAKiC,SAAS,EAAE;IAEvEd,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMe,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpB,YAAY,CAAC;IAErC,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmCxC,MAAM,WAAWC,OAAO,WAAWC,WAAW,EAAE,EAAE;QAChHuC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,MAAM,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QACpC;QACAjD,OAAO,CAACgD,MAAM,CAACE,UAAU,CAAC;QAC1B/B,YAAY,CAAC,KAAK,CAAC;QACnBE,cAAc,CAAC,KAAK,CAAC;QACrBM,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,MAAM;QACLwB,OAAO,CAACC,KAAK,CAAC,eAAe,CAAC;QAC9BC,KAAK,CAAC,2CAA2C,CAAC;MACpD;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCC,KAAK,CAAC,2CAA2C,CAAC;IACpD,CAAC,SAAS;MACR5B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM6B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAInD,UAAU,EAAE,OAAO,CAAC;;IAExB,IAAID,SAAS,EAAE;MACb;MACAiB,YAAY,CAAC,KAAK,CAAC;MACnBE,cAAc,CAAC,KAAK,CAAC;MACrBS,UAAU,CAAC,MAAM9B,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IACtC,CAAC,MAAM;MACL;MACA2B,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAChClC,cAAc,CAAC,CAACD,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMoC,cAAc,GAAIxC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,GAAG;QACN,OAAO,WAAW;MACpB,KAAK,IAAI;QACP,OAAO,YAAY;MACrB,KAAK,KAAK;QACR,OAAO,aAAa;MACtB,KAAK,KAAK;QACR,OAAO,aAAa;MACtB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAIzC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,SAAS;MAAE;MACpB,KAAK,GAAG;QACN,OAAO,SAAS;MAAE;MACpB,KAAK,IAAI;QACP,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB;QACE,OAAO,SAAS;MAAE;IACtB;EACF,CAAC;EAED,oBACEpB,OAAA;IAAK8D,SAAS,EAAE,oBAAoBxC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;IAAAyC,QAAA,eAC/D/D,OAAA;MACE8D,SAAS,EAAE,sBAAsBxC,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;MAC9D0C,KAAK,EAAE3D,cAAc,GAAG;QACtB;QACA4D,eAAe,EAAE,GAAG5D,cAAc,CAAC6D,CAAC,MAAM7D,cAAc,CAAC8D,CAAC;MAC5D,CAAC,GAAG,CAAC,CAAE;MAAAJ,QAAA,gBAEP/D,OAAA;QAAQ8D,SAAS,EAAC,cAAc;QAACM,OAAO,EAAEhC,WAAY;QAAA2B,QAAA,EAAC;MAAC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACjExE,OAAA;QAAK8D,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvC/D,OAAA;UACEyE,GAAG,EAAExD,WAAY;UACjByD,GAAG,EAAExE,SAAS,CAACyE,IAAK;UACpBb,SAAS,EAAC;QAAoB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,EACDnD,eAAe,iBACdrB,OAAA;UACEyE,GAAG,EAAEpD,eAAgB;UACrBqD,GAAG,EAAE,GAAGxE,SAAS,CAACkB,MAAM,QAAS;UACjC0C,SAAS,EAAC;QAAwB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNxE,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,GAC9B,CAACxD,UAAU,IAAI,CAACuB,UAAU,iBACzB9B,OAAA;UACE8D,SAAS,EAAE,gBAAgBxD,SAAS,GAAG,SAAS,GAAG,EAAE,EAAG;UACxD8D,OAAO,EAAEV,WAAY;UAAAK,QAAA,EAEpBzD,SAAS,GAAG,SAAS,GAAG;QAAQ;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CACT,EACA,CAAC1C,UAAU,iBACV9B,OAAA;UACE8D,SAAS,EAAC,gBAAgB;UAC1BM,OAAO,EAAET,mBAAoB;UAC7BK,KAAK,EAAE;YACLY,eAAe,EAAEf,cAAc,CAAC3D,SAAS,CAACkB,MAAM,CAAC;YACjDyD,SAAS,EAAE,YAAYhB,cAAc,CAAC3D,SAAS,CAACkB,MAAM,CAAC;UACzD,CAAE;UAAA2C,QAAA,EACH;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL1C,UAAU,IAAI,CAACvB,UAAU,iBACxBP,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B/D,OAAA;UAAA+D,QAAA,EAAI;QAAiB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1BxE,OAAA;UAAA+D,QAAA,EAAG;QAAoE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAE3ExE,OAAA;UACE8E,IAAI,EAAC,MAAM;UACXC,GAAG,EAAE/C,YAAa;UAClBgD,QAAQ,EAAE3C,gBAAiB;UAC3B4C,MAAM,EAAC,SAAS;UAChBjB,KAAK,EAAE;YAAEkB,OAAO,EAAE;UAAO;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eAEFxE,OAAA;UAAK8D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B/D,OAAA;YACE8D,SAAS,EAAC,sBAAsB;YAChCM,OAAO,EAAEA,CAAA;cAAA,IAAAe,qBAAA;cAAA,QAAAA,qBAAA,GAAMnD,YAAY,CAACoD,OAAO,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC;YAAA,CAAC;YAC7CC,QAAQ,EAAE1D,SAAU;YAAAmC,QAAA,EAEnBrC,YAAY,GAAG,cAAc,GAAG;UAAc;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,EAER9C,YAAY,iBACX1B,OAAA;YAAK8D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC/D,OAAA;cAAA+D,QAAA,GAAG,YAAU,EAACrC,YAAY,CAAC6D,IAAI;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpCxE,OAAA;cAAK8D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B/D,OAAA;gBACE8D,SAAS,EAAC,uBAAuB;gBACjCM,OAAO,EAAE1B,YAAa;gBACtB4C,QAAQ,EAAE1D,SAAU;gBAAAmC,QAAA,EAEnBnC,SAAS,GAAG,cAAc,GAAG;cAAgB;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACTxE,OAAA;gBACE8D,SAAS,EAAC,sBAAsB;gBAChCM,OAAO,EAAEA,CAAA,KAAM;kBACbrC,aAAa,CAAC,KAAK,CAAC;kBACpBJ,eAAe,CAAC,IAAI,CAAC;gBACvB,CAAE;gBACF2D,QAAQ,EAAE1D,SAAU;gBAAAmC,QAAA,EACrB;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAEAhD,WAAW,iBACVxB,OAAA;QACE8D,SAAS,EAAC,2BAA2B;QACrCM,OAAO,EAAEA,CAAA,KAAM3C,cAAc,CAAC,KAAK,CAAE;QAAAsC,QAAA,eAErC/D,OAAA;UACE8D,SAAS,EAAC,2BAA2B;UACrCM,OAAO,EAAGoB,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAC;UAAA;UAAA1B,QAAA,gBAErC/D,OAAA;YAAA+D,QAAA,gBAAG/D,OAAA;cAAQgE,KAAK,EAAE;gBAAE0B,KAAK,EAAE7B,cAAc,CAAC3D,SAAS,CAACkB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAxE,OAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACtE,SAAS,CAACyE,IAAI;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvGxE,OAAA;YAAA+D,QAAA,gBAAG/D,OAAA;cAAQgE,KAAK,EAAE;gBAAE0B,KAAK,EAAE7B,cAAc,CAAC3D,SAAS,CAACkB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAxE,OAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACtE,SAAS,CAACyF,MAAM;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3GxE,OAAA;YAAA+D,QAAA,gBAAG/D,OAAA;cAAQgE,KAAK,EAAE;gBAAE0B,KAAK,EAAE7B,cAAc,CAAC3D,SAAS,CAACkB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAxE,OAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACZ,cAAc,CAAC1D,SAAS,CAACkB,MAAM,CAAC;UAAA;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1HxE,OAAA;YAAA+D,QAAA,gBAAG/D,OAAA;cAAQgE,KAAK,EAAE;gBAAE0B,KAAK,EAAE7B,cAAc,CAAC3D,SAAS,CAACkB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAxE,OAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACtE,SAAS,CAAC0F,WAAW,IAAI,0BAA0B;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxJxE,OAAA;YAAA+D,QAAA,gBAAG/D,OAAA;cAAQgE,KAAK,EAAE;gBAAE0B,KAAK,EAAE7B,cAAc,CAAC3D,SAAS,CAACkB,MAAM;cAAE,CAAE;cAAA2C,QAAA,EAAC;YAAmB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAAAxE,OAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAACtE,SAAS,CAAC2F,UAAU,IAAI,MAAM;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAnQIV,eAAe;AAAA6F,EAAA,GAAf7F,eAAe;AAqQrB,eAAeA,eAAe;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}