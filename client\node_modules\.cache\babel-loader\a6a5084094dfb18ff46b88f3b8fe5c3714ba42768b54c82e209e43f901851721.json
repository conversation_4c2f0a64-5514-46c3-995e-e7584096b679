{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\BingoSquare.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BingoSquare = ({\n  character,\n  isMarked,\n  onClick,\n  onPortraitClick,\n  index,\n  isReadOnly,\n  userImage\n}) => {\n  // Determine the border color based on rarity\n  const getRarityColor = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return '#4CAF50';\n      // Green\n      case 'R':\n        return '#2196F3';\n      // Blue\n      case 'SR':\n        return '#9C27B0';\n      // Purple\n      case 'SSR':\n        return '#FF9800';\n      // Orange\n      case 'UR+':\n        return '#F44336';\n      // Red\n      default:\n        return '#FFFFFF';\n      // White\n    }\n  };\n\n  // Get the number of stars based on rarity\n  const getRarityStars = rarity => {\n    switch (rarity) {\n      case 'FREE':\n        return 1;\n      // Green - 1 star\n      case 'R':\n        return 2;\n      // Blue - 2 stars\n      case 'SR':\n        return 3;\n      // Purple - 3 stars\n      case 'SSR':\n        return 4;\n      // Orange - 4 stars\n      case 'UR+':\n        return 5;\n      // Red - 5 stars\n      default:\n        return 0;\n    }\n  };\n\n  // Get the local file path for the thumbnail\n  const getThumbnailUrl = thumbnailPath => {\n    if (!thumbnailPath) return null;\n\n    // Extract just the filename from the path\n    const filename = thumbnailPath.split('/').pop();\n\n    // Construct the URL to the thumbnail in the public folder\n    return `${process.env.PUBLIC_URL}/thumbnails/${filename}`;\n  };\n\n  // Get the thumbnail URL using the thumbnail path or user image\n  const thumbnailUrl = isMarked && userImage ? `${process.env.PUBLIC_URL}${userImage}` : getThumbnailUrl(character.Thumbnail);\n\n  // Handle click on the thumbnail to show portrait\n  const handleThumbnailClick = e => {\n    // No need to stop propagation since we're removing the square's onClick handler\n\n    // Get the position of the clicked thumbnail for zoom effect\n    const rect = e.currentTarget.getBoundingClientRect();\n    const position = {\n      x: rect.left + rect.width / 2,\n      y: rect.top + rect.height / 2\n    };\n\n    // Pass the character, position, and index to the parent component\n    onPortraitClick(character, position, index);\n  };\n\n  // Add 'free' class if this is the FREE square\n  const isFreeSquare = character.rarity === 'FREE';\n\n  // Get the number of stars for this rarity\n  const starCount = getRarityStars(character.rarity);\n\n  // Get the color for the stars (same as border color)\n  const starColor = getRarityColor(character.rarity);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bingo-square ${isMarked ? 'marked' : ''} ${isFreeSquare ? 'free' : ''} ${isReadOnly ? 'read-only' : ''}`,\n    style: {\n      borderColor: starColor,\n      backgroundColor: 'transparent',\n      position: 'relative',\n      cursor: 'default' // Border area is not clickable\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"thumbnail-container\",\n      onClick: handleThumbnailClick,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: thumbnailUrl,\n        alt: character.Name,\n        className: \"character-thumbnail\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rarity-stars\",\n        children: [...Array(starCount)].map((_, i) => {\n          // Calculate if this is the center star\n          const isCenterStar = i === Math.floor(starCount / 2);\n\n          // For even numbers of stars, make the two middle stars slightly larger\n          const isMiddlePair = starCount % 2 === 0 && (i === starCount / 2 - 1 || i === starCount / 2);\n\n          // Calculate distance from center for graduated sizing\n          const distanceFromCenter = Math.abs(i - (starCount - 1) / 2);\n          // Increase the size difference between center and outer stars\n          const sizeMultiplier = 1 - distanceFromCenter * 0.25;\n          return /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `star ${isCenterStar ? 'center-star' : ''} ${isMiddlePair ? 'middle-pair' : ''}`,\n            style: {\n              color: starColor,\n              fontSize: `${sizeMultiplier * 100}%`,\n              transform: `scale(${sizeMultiplier})`\n            },\n            children: \"\\u2605\"\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_c = BingoSquare;\nexport default BingoSquare;\nvar _c;\n$RefreshReg$(_c, \"BingoSquare\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "BingoSquare", "character", "isMarked", "onClick", "onPortraitClick", "index", "isReadOnly", "userImage", "getRarityColor", "rarity", "getRarityStars", "getThumbnailUrl", "thumbnail<PERSON>ath", "filename", "split", "pop", "process", "env", "PUBLIC_URL", "thumbnailUrl", "<PERSON><PERSON><PERSON><PERSON>", "handleThumbnailClick", "e", "rect", "currentTarget", "getBoundingClientRect", "position", "x", "left", "width", "y", "top", "height", "isFreeSquare", "starCount", "starColor", "className", "style", "borderColor", "backgroundColor", "cursor", "children", "src", "alt", "Name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "isCenterStar", "Math", "floor", "isMiddlePair", "distanceFromCenter", "abs", "sizeMultiplier", "color", "fontSize", "transform", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/BingoSquare.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst BingoSquare = ({ character, isMarked, onClick, onPortraitClick, index, isReadOnly, userImage }) => {\r\n  // Determine the border color based on rarity\r\n  const getRarityColor = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return '#4CAF50'; // Green\r\n      case 'R':\r\n        return '#2196F3'; // Blue\r\n      case 'SR':\r\n        return '#9C27B0'; // Purple\r\n      case 'SSR':\r\n        return '#FF9800'; // Orange\r\n      case 'UR+':\r\n        return '#F44336'; // Red\r\n      default:\r\n        return '#FFFFFF'; // White\r\n    }\r\n  };\r\n\r\n  // Get the number of stars based on rarity\r\n  const getRarityStars = (rarity) => {\r\n    switch (rarity) {\r\n      case 'FREE':\r\n        return 1; // Green - 1 star\r\n      case 'R':\r\n        return 2; // Blue - 2 stars\r\n      case 'SR':\r\n        return 3; // Purple - 3 stars\r\n      case 'SSR':\r\n        return 4; // Orange - 4 stars\r\n      case 'UR+':\r\n        return 5; // Red - 5 stars\r\n      default:\r\n        return 0;\r\n    }\r\n  };\r\n\r\n  // Get the local file path for the thumbnail\r\n  const getThumbnailUrl = (thumbnailPath) => {\r\n    if (!thumbnailPath) return null;\r\n\r\n    // Extract just the filename from the path\r\n    const filename = thumbnailPath.split('/').pop();\r\n\r\n    // Construct the URL to the thumbnail in the public folder\r\n    return `${process.env.PUBLIC_URL}/thumbnails/${filename}`;\r\n  };\r\n\r\n  // Get the thumbnail URL using the thumbnail path or user image\r\n  const thumbnailUrl = (isMarked && userImage)\r\n    ? `${process.env.PUBLIC_URL}${userImage}`\r\n    : getThumbnailUrl(character.Thumbnail);\r\n\r\n  // Handle click on the thumbnail to show portrait\r\n  const handleThumbnailClick = (e) => {\r\n    // No need to stop propagation since we're removing the square's onClick handler\r\n\r\n    // Get the position of the clicked thumbnail for zoom effect\r\n    const rect = e.currentTarget.getBoundingClientRect();\r\n    const position = {\r\n      x: rect.left + rect.width / 2,\r\n      y: rect.top + rect.height / 2\r\n    };\r\n\r\n    // Pass the character, position, and index to the parent component\r\n    onPortraitClick(character, position, index);\r\n  };\r\n\r\n  // Add 'free' class if this is the FREE square\r\n  const isFreeSquare = character.rarity === 'FREE';\r\n\r\n  // Get the number of stars for this rarity\r\n  const starCount = getRarityStars(character.rarity);\r\n\r\n  // Get the color for the stars (same as border color)\r\n  const starColor = getRarityColor(character.rarity);\r\n\r\n  return (\r\n    <div\r\n      className={`bingo-square ${isMarked ? 'marked' : ''} ${isFreeSquare ? 'free' : ''} ${isReadOnly ? 'read-only' : ''}`}\r\n      style={{\r\n        borderColor: starColor,\r\n        backgroundColor: 'transparent',\r\n        position: 'relative',\r\n        cursor: 'default' // Border area is not clickable\r\n      }}\r\n    >\r\n      <div className=\"thumbnail-container\" onClick={handleThumbnailClick}>\r\n        <img\r\n          src={thumbnailUrl}\r\n          alt={character.Name}\r\n          className=\"character-thumbnail\"\r\n        />\r\n        {/* Star rating system - only show on thumbnails, not portraits */}\r\n        <div className=\"rarity-stars\">\r\n          {[...Array(starCount)].map((_, i) => {\r\n            // Calculate if this is the center star\r\n            const isCenterStar = i === Math.floor(starCount / 2);\r\n\r\n            // For even numbers of stars, make the two middle stars slightly larger\r\n            const isMiddlePair = starCount % 2 === 0 && (i === starCount / 2 - 1 || i === starCount / 2);\r\n\r\n            // Calculate distance from center for graduated sizing\r\n            const distanceFromCenter = Math.abs(i - (starCount - 1) / 2);\r\n            // Increase the size difference between center and outer stars\r\n            const sizeMultiplier = 1 - (distanceFromCenter * 0.25);\r\n\r\n            return (\r\n              <span\r\n                key={i}\r\n                className={`star ${isCenterStar ? 'center-star' : ''} ${isMiddlePair ? 'middle-pair' : ''}`}\r\n                style={{\r\n                  color: starColor,\r\n                  fontSize: `${sizeMultiplier * 100}%`,\r\n                  transform: `scale(${sizeMultiplier})`\r\n                }}\r\n              >\r\n                ★\r\n              </span>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BingoSquare;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ;EAAEC,OAAO;EAAEC,eAAe;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EACvG;EACA,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,SAAS;MAAE;MACpB,KAAK,GAAG;QACN,OAAO,SAAS;MAAE;MACpB,KAAK,IAAI;QACP,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB,KAAK,KAAK;QACR,OAAO,SAAS;MAAE;MACpB;QACE,OAAO,SAAS;MAAE;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAID,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,MAAM;QACT,OAAO,CAAC;MAAE;MACZ,KAAK,GAAG;QACN,OAAO,CAAC;MAAE;MACZ,KAAK,IAAI;QACP,OAAO,CAAC;MAAE;MACZ,KAAK,KAAK;QACR,OAAO,CAAC;MAAE;MACZ,KAAK,KAAK;QACR,OAAO,CAAC;MAAE;MACZ;QACE,OAAO,CAAC;IACZ;EACF,CAAC;;EAED;EACA,MAAME,eAAe,GAAIC,aAAa,IAAK;IACzC,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;;IAE/B;IACA,MAAMC,QAAQ,GAAGD,aAAa,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;;IAE/C;IACA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,eAAeL,QAAQ,EAAE;EAC3D,CAAC;;EAED;EACA,MAAMM,YAAY,GAAIjB,QAAQ,IAAIK,SAAS,GACvC,GAAGS,OAAO,CAACC,GAAG,CAACC,UAAU,GAAGX,SAAS,EAAE,GACvCI,eAAe,CAACV,SAAS,CAACmB,SAAS,CAAC;;EAExC;EACA,MAAMC,oBAAoB,GAAIC,CAAC,IAAK;IAClC;;IAEA;IACA,MAAMC,IAAI,GAAGD,CAAC,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACpD,MAAMC,QAAQ,GAAG;MACfC,CAAC,EAAEJ,IAAI,CAACK,IAAI,GAAGL,IAAI,CAACM,KAAK,GAAG,CAAC;MAC7BC,CAAC,EAAEP,IAAI,CAACQ,GAAG,GAAGR,IAAI,CAACS,MAAM,GAAG;IAC9B,CAAC;;IAED;IACA5B,eAAe,CAACH,SAAS,EAAEyB,QAAQ,EAAErB,KAAK,CAAC;EAC7C,CAAC;;EAED;EACA,MAAM4B,YAAY,GAAGhC,SAAS,CAACQ,MAAM,KAAK,MAAM;;EAEhD;EACA,MAAMyB,SAAS,GAAGxB,cAAc,CAACT,SAAS,CAACQ,MAAM,CAAC;;EAElD;EACA,MAAM0B,SAAS,GAAG3B,cAAc,CAACP,SAAS,CAACQ,MAAM,CAAC;EAElD,oBACEV,OAAA;IACEqC,SAAS,EAAE,gBAAgBlC,QAAQ,GAAG,QAAQ,GAAG,EAAE,IAAI+B,YAAY,GAAG,MAAM,GAAG,EAAE,IAAI3B,UAAU,GAAG,WAAW,GAAG,EAAE,EAAG;IACrH+B,KAAK,EAAE;MACLC,WAAW,EAAEH,SAAS;MACtBI,eAAe,EAAE,aAAa;MAC9Bb,QAAQ,EAAE,UAAU;MACpBc,MAAM,EAAE,SAAS,CAAC;IACpB,CAAE;IAAAC,QAAA,eAEF1C,OAAA;MAAKqC,SAAS,EAAC,qBAAqB;MAACjC,OAAO,EAAEkB,oBAAqB;MAAAoB,QAAA,gBACjE1C,OAAA;QACE2C,GAAG,EAAEvB,YAAa;QAClBwB,GAAG,EAAE1C,SAAS,CAAC2C,IAAK;QACpBR,SAAS,EAAC;MAAqB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAEFjD,OAAA;QAAKqC,SAAS,EAAC,cAAc;QAAAK,QAAA,EAC1B,CAAC,GAAGQ,KAAK,CAACf,SAAS,CAAC,CAAC,CAACgB,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;UACnC;UACA,MAAMC,YAAY,GAAGD,CAAC,KAAKE,IAAI,CAACC,KAAK,CAACrB,SAAS,GAAG,CAAC,CAAC;;UAEpD;UACA,MAAMsB,YAAY,GAAGtB,SAAS,GAAG,CAAC,KAAK,CAAC,KAAKkB,CAAC,KAAKlB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,SAAS,GAAG,CAAC,CAAC;;UAE5F;UACA,MAAMuB,kBAAkB,GAAGH,IAAI,CAACI,GAAG,CAACN,CAAC,GAAG,CAAClB,SAAS,GAAG,CAAC,IAAI,CAAC,CAAC;UAC5D;UACA,MAAMyB,cAAc,GAAG,CAAC,GAAIF,kBAAkB,GAAG,IAAK;UAEtD,oBACE1D,OAAA;YAEEqC,SAAS,EAAE,QAAQiB,YAAY,GAAG,aAAa,GAAG,EAAE,IAAIG,YAAY,GAAG,aAAa,GAAG,EAAE,EAAG;YAC5FnB,KAAK,EAAE;cACLuB,KAAK,EAAEzB,SAAS;cAChB0B,QAAQ,EAAE,GAAGF,cAAc,GAAG,GAAG,GAAG;cACpCG,SAAS,EAAE,SAASH,cAAc;YACpC,CAAE;YAAAlB,QAAA,EACH;UAED,GATOW,CAAC;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASF,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACe,EAAA,GA7HI/D,WAAW;AA+HjB,eAAeA,WAAW;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}