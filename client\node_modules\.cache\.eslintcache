[{"C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js": "4", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js": "5", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js": "6", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js": "7", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js": "8", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js": "9", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Cards\\Cards.js": "10", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js": "11", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js": "12", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js": "13", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js": "14", "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js": "15"}, {"size": 535, "mtime": 1747521110957, "results": "16", "hashOfConfig": "17"}, {"size": 5674, "mtime": 1749445792105, "results": "18", "hashOfConfig": "17"}, {"size": 362, "mtime": 1747521110970, "results": "19", "hashOfConfig": "17"}, {"size": 4385, "mtime": 1747885973072, "results": "20", "hashOfConfig": "17"}, {"size": 9753, "mtime": 1749093515268, "results": "21", "hashOfConfig": "17"}, {"size": 3167, "mtime": 1749093515257, "results": "22", "hashOfConfig": "17"}, {"size": 4136, "mtime": 1749093515222, "results": "23", "hashOfConfig": "17"}, {"size": 3819, "mtime": 1749093515247, "results": "24", "hashOfConfig": "17"}, {"size": 6251, "mtime": 1749285855260, "results": "25", "hashOfConfig": "17"}, {"size": 4677, "mtime": 1749159953151, "results": "26", "hashOfConfig": "17"}, {"size": 10801, "mtime": 1749164464798, "results": "27", "hashOfConfig": "17"}, {"size": 10856, "mtime": 1749252121137, "results": "28", "hashOfConfig": "17"}, {"size": 4378, "mtime": 1749164103947, "results": "29", "hashOfConfig": "17"}, {"size": 745, "mtime": 1747885973100, "results": "30", "hashOfConfig": "17"}, {"size": 3915, "mtime": 1747885973113, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10mvb5b", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\UserContext.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Dashboard\\UserDashboard.js", ["77", "78", "79"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\Leaderboard.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BoardViewer.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\HowToPlay\\HowToPlay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Cards\\Cards.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoBoard.js", ["80"], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PortraitOverlay.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\BingoSquare.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\ConfirmationModal.js", [], [], "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\src\\components\\Board\\PointsDisplay.js", [], [], {"ruleId": "81", "severity": 1, "message": "82", "line": 63, "column": 6, "nodeType": "83", "endLine": 63, "endColumn": 12, "suggestions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 72, "column": 9, "nodeType": "87", "messageId": "88", "endLine": 72, "endColumn": 20}, {"ruleId": "85", "severity": 1, "message": "89", "line": 134, "column": 9, "nodeType": "87", "messageId": "88", "endLine": 134, "endColumn": 27}, {"ruleId": "81", "severity": 1, "message": "90", "line": 80, "column": 6, "nodeType": "83", "endLine": 80, "endColumn": 72, "suggestions": "91"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'navigate'. Either include it or remove the dependency array.", "ArrayExpression", ["92"], "no-unused-vars", "'viewMyBoard' is assigned a value but never used.", "Identifier", "unusedVar", "'handleRefreshClick' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'calculateTotalPoints'. Either include it or remove the dependency array.", ["93"], {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, "Update the dependencies array to be: [navigate, user]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [markedCells, userImages, isReadOnly, userId, boardId, characters, calculateTotalPoints]", {"range": "100", "text": "101"}, [2352, 2358], "[navigate, user]", [2821, 2887], "[markedCells, userImages, isReadOnly, userId, boardId, characters, calculateTotalPoints]"]